# GeoDaedalus Environment Configuration
# Copy this file to .env and fill in your API keys and settings
# Usage: cp env.example .env && edit .env

# Application Settings
DEBUG=false
ENVIRONMENT=development
APP_NAME=GeoDaedalus
VERSION=0.1.0

# Data Directories
DATA_DIR=./data
OUTPUT_DIR=./output
CACHE_DIR=./cache

# API Keys - Get these from respective service providers
OPENAI_API_KEY=your_openai_api_key_here
SERPAPI_KEY=your_serpapi_key_here

# LLM Configuration (prefix: LLM_)
LLM_PROVIDER=openai
LLM_MODEL=gpt-3.5-turbo
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=4096
LLM_TIMEOUT=30
LLM_API_KEY=${OPENAI_API_KEY}

# Search Configuration (prefix: SEARCH_)
SEARCH_DEFAULT_ENGINE=google
SEARCH_MAX_RESULTS=10
SEARCH_TIMEOUT=30
SEARCH_SERPAPI_KEY=${SERPAPI_KEY}

# Processing Configuration (prefix: PROCESSING_)
PROCESSING_MAX_FILE_SIZE=104857600  # 100MB in bytes
PROCESSING_MAX_FILE_SIZE_MB=100
PROCESSING_MAX_PDF_PAGES=100
PROCESSING_CHUNK_SIZE=1000
PROCESSING_CHUNK_OVERLAP=200

# Logging Configuration (prefix: LOG_)
LOG_LEVEL=INFO
LOG_FORMAT=structured
LOG_ENABLE_RICH=true

# Metrics Configuration (prefix: METRICS_)
METRICS_ENABLED=true
METRICS_TRACK_EXECUTION_TIME=true
METRICS_TRACK_TOKEN_USAGE=true
METRICS_TRACK_API_COSTS=true
METRICS_EXPORT_INTERVAL=3600

# Cache Configuration (prefix: CACHE_)
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
CACHE_BACKEND=memory

# Database Configuration (prefix: DB_)
DB_URL=sqlite:///./geodaedalus.db
DB_ECHO=false
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10 